import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/contact_info/contact_action_buttons.dart';

/// Contact info popup component displayed as bottom modal sheet
/// Shows user profile picture, name, contact actions and details
class ContactInfoPopup extends StatelessWidget {
  final String userName;
  final String userImage;
  final String userEmail;
  final String userPhone;
  final VoidCallback? onCall;
  final VoidCallback? onChat;
  final VoidCallback? onEmail;

  const ContactInfoPopup({
    super.key,
    required this.userName,
    required this.userImage,
    required this.userEmail,
    required this.userPhone,
    this.onCall,
    this.onChat,
    this.onEmail,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Profile picture - rectangular with rounded corners
          Container(
            width: MediaQuery.of(context).size.width * 0.8,
            height: 200,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: colors.primaryVariant,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Image.asset(
                userImage,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback to initials if image fails to load
                  return Container(
                    color: colors.primary.withOpacity(0.1),
                    child: Center(
                      child: Text(
                        userName.isNotEmpty ? userName[0].toUpperCase() : 'U',
                        style: textStyles.headline.copyWith(
                          color: colors.primary,
                          fontSize: 48,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // User name - left aligned with primary color
          Container(
            width: double.infinity,
            margin: const EdgeInsets.only(top: 24),
            child: Text(
              userName,
              style: textStyles.headline3.copyWith(
                color: colors.primary,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.left,
            ),
          ),

          // Action buttons (Call, Chat, Email) - in single container with primary variant background
          Container(
            margin: const EdgeInsets.only(top: 24),
            decoration: BoxDecoration(
              color: colors.primaryVariant,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ContactActionButtons(
              onCall: onCall,
              onChat: onChat,
              onEmail: onEmail,
            ),
          ),

          // Contact details - separate containers for email and phone
          Container(
            margin: const EdgeInsets.only(top: 24),
            child: Column(
              children: [
                // Email container with border
                _buildContactContainer(
                  context,
                  icon: Icons.email_outlined,
                  label: Labels.email,
                  value: userEmail,
                ),

                // Spacing between containers
                Container(
                  margin: const EdgeInsets.only(top: 16),
                ),

                // Phone container with border
                _buildContactContainer(
                  context,
                  icon: Icons.phone_outlined,
                  label: Labels.phone,
                  value: userPhone,
                ),
              ],
            ),
          ),

          // Close button - positioned at bottom with more spacing
          Container(
            margin: const EdgeInsets.only(top: 40, bottom: 16),
            width: double.infinity,
            height: 48,
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: colors.strokeColor,
                  width: 1,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                Labels.close,
                style: textStyles.body.copyWith(
                  color: colors.primaryText,
                ),
              ),
            ),
          ),

          // Bottom safe area
          Container(
            height: MediaQuery.of(context).padding.bottom,
          ),
        ],
      ),
    );
  }

  /// Helper method to build contact detail containers with border, icon, label and value
  Widget _buildContactContainer(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.strokeColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Icon
          Container(
            width: 24,
            height: 24,
            child: Icon(
              icon,
              size: 20,
              color: colors.tertiaryText,
            ),
          ),

          // Label and value
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(left: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: textStyles.body3.copyWith(
                      color: colors.tertiaryText,
                      fontSize: 12,
                    ),
                  ),
                  Container(
                    margin: const EdgeInsets.only(top: 2),
                    child: Text(
                      value,
                      style: textStyles.body.copyWith(
                        color: colors.primaryText,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
