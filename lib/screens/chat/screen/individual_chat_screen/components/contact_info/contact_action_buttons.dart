import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';

/// Contact action buttons component
/// Displays Call, Chat, and Email buttons in a horizontal row
class ContactActionButtons extends StatelessWidget {
  final VoidCallback? onCall;
  final VoidCallback? onChat;
  final VoidCallback? onEmail;

  const ContactActionButtons({
    super.key,
    this.onCall,
    this.onChat,
    this.onEmail,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Call button
        _buildActionButton(
          context,
          icon: Icons.phone,
          label: Labels.call,
          onTap: onCall,
        ),

        // Chat button
        _buildActionButton(
          context,
          icon: Icons.chat,
          label: Labels.chat,
          onTap: onChat,
        ),

        // Email button
        _buildActionButton(
          context,
          icon: Icons.email,
          label: Labels.email,
          onTap: onEmail,
        ),
      ],
    );
  }

  /// Helper method to build individual action buttons
  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: onTap,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: Column(
                children: [
                  // Icon
                  Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: colors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      size: 20,
                      color: Colors.white,
                    ),
                  ),

                  // Label
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    child: Text(
                      label,
                      style: textStyles.body3.copyWith(
                        color: colors.primaryText,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
