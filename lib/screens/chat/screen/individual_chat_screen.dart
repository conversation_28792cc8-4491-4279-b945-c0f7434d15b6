import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_header.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_messages_list.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_input_field.dart';

class IndividualChatScreen extends StatelessWidget {
  final String userName;
  final String userImage;
  final String lastSeen;
  final bool isOnline;

  const IndividualChatScreen({
    super.key,
    required this.userName,
    required this.userImage,
    required this.lastSeen,
    this.isOnline = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return Scaffold(
      backgroundColor: colors.background,
      // Hide bottom navigation bar by not using shell route
      body: Column(
        children: [
          // Chat Header with back button, user info
          ChatHeader(
            userName: userName,
            userImage: userImage,
            lastSeen: lastSeen,
            isOnline: isOnline,
            onBackPressed: () => context.pop(),
          ),

          // Messages List - Expandable to take remaining space
          Expanded(
            child: ChatMessagesList(),
          ),

          // Input field at bottom
          ChatInputField(
            onSendMessage: (message) {
              // Handle sending message
              // TODO: Implement message sending logic
              print('Message sent: $message');
            },
            onAttachmentPressed: () {
              // Handle attachment
              print('Attachment pressed');
            },
            onVoicePressed: () {
              // Handle voice message
              print('Voice message pressed');
            },
          ),
        ],
      ),
    );
  }
}
