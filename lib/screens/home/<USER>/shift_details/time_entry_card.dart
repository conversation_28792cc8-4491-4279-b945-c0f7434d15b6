import 'package:ako_basma/styles/colors.dart';
import 'package:flutter/material.dart';
import 'package:remixicon/remixicon.dart';
// import 'package:time_picker_with_timezone/time_picker_with_timezone.dart';

class TimeEntryCard extends StatelessWidget {
  final String title;
  final String time;
  final String location;
  final VoidCallback? onEdit;
  final Function(String)? onTimeChanged;

  const TimeEntryCard({
    super.key,
    required this.title,
    required this.time,
    required this.location,
    this.onEdit,
    this.onTimeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;

    return Container(
      width: screenWidth - 32, // Fill (328px)
      height: 100, // check this once again
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: colors(context).brightness == Brightness.light
            ? DesignColors.lightBackgroundContainer
            : DesignColors.darkBackgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          width: 1,
          color: colors(context).brightness == Brightness.light
              ? DesignColors.lightPrimaryVariant
              : DesignColors.darkPrimaryVariant,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with title and edit icon
          Container(
            padding: const EdgeInsets.only(
              left: 12,
              right: 12,
              top: 8,
              bottom: 4, // Gap
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: textStyles(context).labelLarge?.copyWith(
                          color: colors(context).brightness == Brightness.light
                              ? DesignColors.lightSecondaryText
                              : DesignColors.darkSecondaryText,
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                  ),
                ),
                GestureDetector(
                  onTap: () => _showTimePicker(context),
                  child: Container(
                    child: Icon(
                      // TODO - replace this icon with the figma design icon
                      Remix.pencil_line,
                      color: colors(context).primary,
                      size: 18,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Time
          Container(
            padding: const EdgeInsets.only(
              left: 12,
              right: 12,
              bottom: 4,
              top: 4,
            ),
            alignment: Alignment.centerLeft,
            child: Text(
              time,
              style: textStyles(context).titleLarge?.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: colors(context).brightness == Brightness.light
                        ? DesignColors.lightPrimaryText
                        : DesignColors.darkPrimaryText,
                    height: 1.2,
                  ),
            ),
          ),

          Container(
            margin: const EdgeInsets.only(top: 6),
          ),

          // Location
          Container(
            padding: const EdgeInsets.only(
              left: 12,
              right: 12,
              bottom: 8,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Location icon
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: colors(context).primaryContainer,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.location_on_outlined,
                    color: colors(context).primary,
                    size: 14,
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(left: 8),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    location,
                    style: textStyles(context).bodyMedium?.copyWith(
                          color: colors(context).brightness == Brightness.light
                              ? DesignColors.lightSecondaryText
                              : DesignColors.darkSecondaryText,
                          fontSize: 10,
                        ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // code for rounded time picker

  void _showTimePicker(BuildContext context) async {
    final TimeOfDay? selectedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            timePickerTheme: TimePickerThemeData(
              backgroundColor: colors(context).brightness == Brightness.light
                  ? Colors.white
                  : Colors.black,
              hourMinuteShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              dayPeriodShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              dayPeriodColor: WidgetStateColor.resolveWith(
                  (states) => states.contains(WidgetState.selected)
                      ? colors(context).primary
                      : colors(context).brightness == Brightness.light
                          ? DesignColors.lightPrimaryVariant
                          : DesignColors.darkSurface),
              dayPeriodTextColor: WidgetStateColor.resolveWith(
                  (states) => states.contains(WidgetState.selected)
                      ? colors(context).onPrimary
                      : colors(context).brightness == Brightness.light
                          ? DesignColors.lightPrimaryText
                          : DesignColors.darkPrimaryText),
              hourMinuteColor: WidgetStateColor.resolveWith(
                  (states) => states.contains(WidgetState.selected)
                      ? colors(context).primary
                      : colors(context).brightness == Brightness.light
                          ? DesignColors.lightPrimaryVariant
                          : DesignColors.darkSurface),
              hourMinuteTextColor: WidgetStateColor.resolveWith(
                  (states) => states.contains(WidgetState.selected)
                      ? colors(context).onPrimary
                      : colors(context).brightness == Brightness.light
                          ? DesignColors.lightPrimaryText
                          : DesignColors.darkPrimaryText),
              dialBackgroundColor:
                  colors(context).brightness == Brightness.light
                      ? Colors.white
                      : DesignColors.darkBackground,
              dialHandColor: colors(context).primary,
              dialTextColor: WidgetStateColor.resolveWith(
                  (states) => states.contains(WidgetState.selected)
                      ? colors(context).onPrimary
                      : colors(context).brightness == Brightness.light
                          ? DesignColors.lightPrimaryText
                          : DesignColors.darkPrimaryText),
              entryModeIconColor: colors(context).primary,
            ),
            colorScheme: ColorScheme(
              brightness: colors(context).brightness,
              primary: colors(context).primary,
              onPrimary: colors(context).onPrimary,
              secondary: colors(context).secondary,
              onSecondary: colors(context).onSecondary,
              error: DesignColors.error,
              onError: colors(context).onError,
              background: colors(context).brightness == Brightness.light
                  ? Colors.white
                  : Colors.black,
              onBackground: colors(context).brightness == Brightness.light
                  ? DesignColors.lightPrimaryText
                  : DesignColors.darkPrimaryText,
              surface: colors(context).brightness == Brightness.light
                  ? DesignColors.lightSurface
                  : DesignColors.darkSurface,
              onSurface: colors(context).brightness == Brightness.light
                  ? DesignColors.lightPrimaryText
                  : DesignColors.darkPrimaryText,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: colors(context).primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedTime != null && onTimeChanged != null) {
      final formattedTime = _formatTime(selectedTime);
      onTimeChanged!(formattedTime);
    }
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }
}
