import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/clock_in.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/clock_in_details.dart';
import 'package:ako_basma/screens/home/<USER>/clock_out/clock_out.dart';

import 'package:ako_basma/screens/home/<USER>/greeting.dart';
import 'package:ako_basma/screens/home/<USER>/location.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/shift_details.dart';
import 'package:ako_basma/components/switch/switch_button_group.dart';
import 'package:ako_basma/screens/home/<USER>/workspace.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _showClockInDetails = false;
  bool _showClockOut = false;
  bool _showWorkspace = false;

  void toggleClockInDetails(bool show) {
    setState(() {
      _showClockInDetails = show;
      _showClockOut = false;
    });
  }

  void toggleClockOut(bool show) {
    setState(() {
      _showClockInDetails = false;
      _showClockOut = show;
    });
  }

  void toggleWorkspace(bool show) {
    setState(() {
      _showWorkspace = show;
      _showClockInDetails = false;
      _showClockOut = false;
    });
  }

  void _showShiftDetailsPopup(BuildContext context) {
    showAdaptivePopup(
      context,
      (ctx, sc) => ShiftDetails(
        onBack: () {
          Navigator.of(ctx).pop();
        },
        resetToClockIn: () {
          // Reset to clock in screen
          setState(() {
            _showClockInDetails = false;
            _showClockOut = false;
            _showWorkspace = false;
          });
        },
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          const Greeting(),
          Container(
            margin: const EdgeInsets.only(top: 10),
            child: SwitchButtonGroup(
              labels: const [Labels.timeClock, Labels.workspace],
              onTap: (index) {
                setState(() {
                  final isTimeClockSelected = index == 0;
                  _showWorkspace = !isTimeClockSelected;
                  if (isTimeClockSelected) {
                    _showClockInDetails = false;
                    _showClockOut = false;
                  }
                });
              },
            ),
          ),
          if (_showWorkspace)
            const Workspace()
          else if (_showClockInDetails)
            ClockInDetailsScreen(
              onBack: () => toggleClockInDetails(false),
              onEndShift: () => toggleClockOut(true),
            )
          else if (_showClockOut)
            Column(
              children: [
                const Location(),
                ClockOut(
                  onClockOutPressed: () {
                    _showShiftDetailsPopup(context);
                  },
                ),
              ],
            )
          else
            Column(
              children: [
                const Location(),
                ClockIn(
                  onClockInPressed: () => toggleClockInDetails(true),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
