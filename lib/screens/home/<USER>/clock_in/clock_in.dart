import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/clock_in_button.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/shift_timings.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';

import 'package:flutter/material.dart';

class ClockIn extends StatefulWidget {
  final VoidCallback? onClockInPressed;

  const ClockIn({
    super.key,
    this.onClockInPressed,
  });

  @override
  State<ClockIn> createState() => _ClockInState();
}

class _ClockInState extends State<ClockIn> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;
    final containerWidth =
        screenWidth - 32; // Full width minus margins (16px on each side)

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      width: containerWidth,
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Padding(
              padding:
                  const EdgeInsets.only(top: 8, right: 12, bottom: 8, left: 12),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: colors.primaryVariant,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.location_on_outlined,
                      color: colors.primary,
                      size: 14,
                    ),
                  ),
                  // Container(
                  //   margin: const EdgeInsets.only(left: 8),
                  //   width: 154,
                  //   height: 16,
                  // ),
                  // TODO - have to look after this text issue as this is cutting the text
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.only(left: 8, right: 8),
                      child: Text(
                        Labels.location,
                        style: textStyles.body3.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: ClockInButton(
              onPressed: widget.onClockInPressed,
            ),
          ),
          Container(
            margin:
                const EdgeInsets.only(top: 16, bottom: 8, left: 12, right: 12),
            child: const ShiftTimings(),
          ),
        ],
      ),
    );
  }
}
