import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/custom_date_pay_card.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/filter_dialog_box.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/timesheet_card.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class TimesheetPopup extends StatefulWidget {
  const TimesheetPopup({super.key});

  @override
  State<TimesheetPopup> createState() => _TimesheetPopupState();
}

class _TimesheetPopupState extends State<TimesheetPopup> {
  String filter = Labels.filter;
  String filterLabel = Labels.filter;
  DateTime? fromDate;
  DateTime? toDate;

  String _monthName(DateTime date) {
    // Use DateFormat if intl is imported, otherwise use a static list
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[date.month - 1];
  }

  // date format helper
  String _formatDateRange(DateTime start, DateTime end) {
    if (start.year == end.year &&
        start.month == end.month &&
        start.day == end.day) {
      return "${start.day.toString().padLeft(2, '0')} ${_monthName(start)} ${start.year}";
    } else {
      return "${start.day.toString().padLeft(2, '0')} ${_monthName(start)} - ${end.day.toString().padLeft(2, '0')} ${_monthName(end)} ${end.year}";
    }
  }

  @override
  void initState() {
    super.initState();
    filter = Labels.filter;
    filterLabel = Labels.filter;
    fromDate = null;
    toDate = null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and title
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        color: colors.primaryText,
                        size: 20,
                      ),
                    ),
                  ),
                  Container(margin: const EdgeInsets.only(left: 16)),
                  Text(
                    Labels.timeSheet,
                    style: textStyles.headline3.copyWith(
                      color: colors.primaryText,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  // Filter dropdown
                  Expanded(
                    flex: 3,
                    child: GestureDetector(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return FilterDialogBox(
                              initialFilter: filter,
                              initialFromDate: fromDate,
                              initialToDate: toDate,
                              onSave: (selectedFilter, selectedFromDate,
                                  selectedToDate) {
                                setState(() {
                                  // when custom date is selected then filter text will be customDateRange as selectedFromDate and selectedToDate will not be null
                                  if (selectedFromDate != null &&
                                      selectedToDate != null) {
                                    filter = Labels.customDateRange;
                                  } else {
                                    filter = selectedFilter;
                                  }
                                  fromDate = selectedFromDate;
                                  toDate = selectedToDate;
                                  // Update label as the filter text will be customDateRange as selectedFromDate and selectedToDate will not be null
                                  if (fromDate != null && toDate != null) {
                                    filterLabel =
                                        _formatDateRange(fromDate!, toDate!);
                                  } else {
                                    filterLabel = filter;
                                  }
                                });
                                Navigator.pop(context);
                              },
                              onCancel: () {
                                Navigator.pop(context);
                              },
                            );
                          },
                        );
                      },
                      child: Container(
                        margin: const EdgeInsets.only(right: 8),
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        height: 48,
                        decoration: BoxDecoration(
                          color: colors.backgroundContainer,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: colors.strokeColor,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              filterLabel, // the selected date range will be shown here but i think the formatting should be changed
                              style: textStyles.body2.copyWith(
                                color: colors.secondaryText,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            Icon(Icons.keyboard_arrow_down,
                                color: colors.primary),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // Today button
                  Expanded(
                    flex: 1,
                    child: SizedBox(
                      height: 50,
                      child: OutlinedButton(
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 0),
                          side: BorderSide(
                            color: colors.primary,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: () {
                          final now = DateTime.now();
                          setState(() {
                            filter = Labels.today;
                            fromDate = now;
                            toDate = now;
                            filterLabel = _formatDateRange(now, now);
                          });
                        },
                        child: Text(
                          Labels.today,
                          style: textStyles.body2.copyWith(
                            color: colors.primary,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Show custom date pay card only for custom date range or today
            if ((filter == Labels.customDateRange || filter == Labels.today) &&
                fromDate != null &&
                toDate != null)
              Container(
                margin: const EdgeInsets.only(top: 8),
                child: const CustomDatePayCard(),
              ),
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                children: [
                  // Approved timesheet card
                  TimesheetCard(
                    location: Labels.location,
                    title: '10:30:00',
                    titleTextStyle: TextStyle(
                      color: colors.primary,
                      fontWeight: FontWeight.w500,
                      fontSize: 32,
                    ),
                    date: fromDate != null
                        ? fromDate!.day.toString().padLeft(2, '0')
                        : '07',
                    month: fromDate != null ? _monthName(fromDate!) : 'Mar',
                    year: fromDate != null ? fromDate!.year.toString() : '2025',
                    status: 'Approved',
                    statusColor: colors.success,
                    statusBgColor: colors.successContainer,
                    showStatus:
                        fromDate != null && toDate != null ? false : true,
                  ),

                  // Pending timesheet
                  TimesheetCard(
                    location: Labels.location,
                    title: (filter == Labels.today) ? "Leave" : "10:30:00",
                    titleTextStyle: textStyles.headline4.copyWith(
                      color: colors.warning,
                      fontWeight: FontWeight.w500,
                      fontSize: 32,
                    ),
                    date: fromDate != null
                        ? fromDate!.day.toString().padLeft(2, '0')
                        : '12',
                    month: fromDate != null ? _monthName(fromDate!) : 'Mar',
                    year: fromDate != null ? fromDate!.year.toString() : '2025',
                    status: "Pending",
                    statusColor: colors.warning,
                    statusBgColor: colors.warningContainer,
                    showStatus:
                        fromDate != null && toDate != null ? false : true,
                  ),

                  // Rejected timesheet
                  TimesheetCard(
                    location: Labels.location,
                    title: "00:00:00",
                    titleTextStyle: textStyles.headline4.copyWith(
                      color: colors.error,
                      fontWeight: FontWeight.w500,
                      fontSize: 32,
                    ),
                    date: fromDate != null
                        ? fromDate!.day.toString().padLeft(2, '0')
                        : '12',
                    month: fromDate != null ? _monthName(fromDate!) : 'Mar',
                    year: fromDate != null ? fromDate!.year.toString() : '2025',
                    status: "Rejected",
                    statusColor: colors.error,
                    statusBgColor: colors.errorContainer,
                    showStatus:
                        fromDate != null && toDate != null ? false : true,
                  ),

                  // Holiday timesheet
                  TimesheetCard(
                    location: Labels.location,
                    title: 'Holiday',
                    titleTextStyle: textStyles.headline4.copyWith(
                      color: colors.info,
                      fontWeight: FontWeight.w500,
                      fontSize: 32,
                    ),
                    date: fromDate != null
                        ? fromDate!.day.toString().padLeft(2, '0')
                        : '12',
                    month: fromDate != null ? _monthName(fromDate!) : 'Mar',
                    year: fromDate != null ? fromDate!.year.toString() : '2025',
                    status: "Holiday",
                    statusColor: colors.info,
                    statusBgColor: colors.infoContainer,
                    showStatus:
                        fromDate != null && toDate != null ? false : true,
                  ),

                  // Approved timesheet
                  TimesheetCard(
                    location: Labels.location,
                    title: '10:30:00',
                    titleTextStyle: textStyles.headline4.copyWith(
                      color: colors.primary,
                      fontWeight: FontWeight.w500,
                      fontSize: 32,
                    ),
                    date: fromDate != null
                        ? fromDate!.day.toString().padLeft(2, '0')
                        : '07',
                    month: fromDate != null ? _monthName(fromDate!) : 'Mar',
                    year: fromDate != null ? fromDate!.year.toString() : '2025',
                    status: 'Approved',
                    statusColor: colors.success,
                    statusBgColor: colors.successContainer,
                    showStatus:
                        fromDate != null && toDate != null ? false : true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
