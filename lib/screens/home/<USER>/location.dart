import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class Location extends StatefulWidget {
  const Location({super.key});

  @override
  State<Location> createState() => _LocationState();
}

class _LocationState extends State<Location> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final screenWidth = MediaQuery.of(context).size.width;
    final containerWidth = screenWidth - 32; // Full width minus margins

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      width: containerWidth,
      height: 191,
      decoration: BoxDecoration(
        // no border
        borderRadius: BorderRadius.circular(8),
        color: colors.surface,
      ),
      child: ClipRRect(
        borderRadius:
            BorderRadius.circular(7), // Slightly smaller to account for border
        child: Stack(
          children: [
            // Use location.png as the background image
            Image.asset(
              Theme.of(context).brightness == Brightness.dark
                  ? 'assets/images/location_dark.png'
                  : 'assets/images/location_light.png',
              width: containerWidth,
              height: 191,
              fit: BoxFit.cover,
            ),

            // User location marker
            Center(
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: colors.primary.withOpacity(0.2),
                ),
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: colors.primary.withOpacity(0.7),
                  ),
                  child: CircleAvatar(
                    radius: 12,
                    backgroundColor: colors.primary,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: const Icon(
                        Icons.person,
                        // color:
                        //     colors.onPrimary, // TODO: check if this is correct
                        size: 16,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
