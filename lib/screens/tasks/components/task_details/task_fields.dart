import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'dropdown_field.dart';
import 'assignee_field.dart';

class TaskFields extends StatelessWidget {
  const TaskFields({super.key});

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).extension<AppColors>()!;
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          height: 1,
          color: colors.strokeColor,
        ),
        Container(height: 20),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: colors.background,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: colors.strokeColor, width: 1),
          ),
          child: Column(
            children: [
              DropdownField(label: 'Status', value: 'Backlog', isStatus: true),
              Container(height: 16),
              DropdownField(
                  label: 'Department', value: 'Design', isDepartment: true),
              Container(height: 16),
              Assign<PERSON><PERSON><PERSON>(label: 'Assignee', showLabel: true),
              Container(height: 12),
              Assignee<PERSON>ield(showLabel: false),
            ],
          ),
        ),
        Container(height: 24),
      ],
    );
  }
}
