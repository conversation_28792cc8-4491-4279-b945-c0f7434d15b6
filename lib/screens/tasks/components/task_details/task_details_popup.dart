import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'popup_header.dart';
import 'main_task_card.dart';

class TaskDetailsPopup extends StatelessWidget {
  final VoidCallback? onBack;

  const TaskDetailsPopup({super.key, this.onBack});

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).extension<AppColors>()!;

    return Scaffold(
      backgroundColor: colors.background,
      body: Safe<PERSON><PERSON>(
        child: Column(
          children: [
            PopupHeader(onBack: onBack),
            const Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: MainTaskCard(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
