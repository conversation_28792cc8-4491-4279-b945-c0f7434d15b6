import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';

class PopupHeader extends StatelessWidget {
  final VoidCallback? onBack;

  const PopupHeader({super.key, this.onBack});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          GestureDetector(
            onTap: onBack ?? () => Navigator.of(context).pop(),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colors.strokeColor,
                  width: 1,
                ),
              ),
              child: Icon(
                Icons.arrow_back_ios_new,
                color: colors.primaryText,
                size: 20,
              ),
            ),
          ),
          Container(margin: const EdgeInsets.only(left: 12)),
          Text(
            'Task details',
            style: textStyles.headline3.copyWith(
              color: colors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }
}
