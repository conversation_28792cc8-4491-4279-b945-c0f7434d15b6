import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/tasks/components/add_task_popup.dart';
import 'package:ako_basma/screens/tasks/components/create_project.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CreateTask extends StatelessWidget {
  final VoidCallback? onBack;
  final VoidCallback? onTaskCreated;
  final bool showAppBar;

  const CreateTask({
    super.key,
    this.onBack,
    this.onTaskCreated,
    this.showAppBar = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (showAppBar) _buildAppBar(context),
        Expanded(
          child: _buildBody(context),
        ),
      ],
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      color: colors.background,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppBar(
            backgroundColor: colors.background,
            title: Row(
              children: [
                Text(
                  Labels.allProjects,
                  style: TextStyle(
                    color: colors.primaryText,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: colors.primary,
                  size: 20,
                ),
              ],
            ),
            titleSpacing: 0,
            leadingWidth: 56,
            leading: Container(
              margin: const EdgeInsets.only(left: 16),
              child: GestureDetector(
                onTap: onBack,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.arrow_back_ios_new,
                    color: colors.primaryText,
                    size: 20,
                  ),
                ),
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.only(right: 16),
                decoration: BoxDecoration(
                  color: colors.backgroundContainer,
                  borderRadius: BorderRadius.circular(6.67),
                  border: Border.all(
                    color: colors.strokeColor,
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: () => _showAddTaskPopup(context),
                  icon: Icon(
                    Icons.add,
                    color: colors.primary,
                  ),
                ),
              ),
            ],
          ),
          Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.only(left: 20, bottom: 8),
            child: Text(
              Labels.manageProjects,
              style: TextStyle(
                color: colors.secondaryText,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showAddTaskPopup(BuildContext context) async {
    final bool? taskCreated = await showAdaptivePopup<bool>(
      context,
      (ctx, sc) => AddTaskPopup(
        onBack: () => Navigator.pop(ctx),
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );

    // If the user saved the task, call the callback
    if (taskCreated == true) {
      onTaskCreated?.call();
    }
  }

  Widget _buildBody(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      color: colors.background,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Empty state icon
            Image.asset(
              'assets/images/create_task.png',
              width: 64,
              height: 64,
              colorBlendMode: BlendMode.srcIn,
              color: colors.primaryText,
            ),
            Container(
              margin: const EdgeInsets.only(top: 16),
            ),
            Text(
              Labels.noTasksFound,
              style: TextStyle(
                color: colors.primaryText,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 8),
            ),
            Text(
              Labels.pleaseCreateATask,
              style: TextStyle(
                color: colors.secondaryText,
                fontSize: 14,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 24),
            ),
            Container(
              margin: const EdgeInsets.symmetric(
                  horizontal:
                      120), // i don't think this is a good way to give width to the button
              child: ElevatedButton.icon(
                onPressed: () => _showAddTaskPopup(context),
                icon: Icon(Icons.add,
                    size: 20, color: theme.colorScheme.onPrimary),
                label: Text(
                  Labels.createTask,
                  style: textStyles.body.copyWith(
                    color: theme.colorScheme.onPrimary,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colors.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                  minimumSize: const Size(double.infinity, 48),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
