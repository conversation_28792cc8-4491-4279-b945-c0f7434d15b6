import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/tasks/components/create_project.dart';
import 'package:ako_basma/screens/tasks/components/create_task.dart';
import 'package:ako_basma/screens/tasks/components/add_task_popup.dart';
import 'package:ako_basma/components/search bar/search_bar.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ako_basma/screens/tasks/components/project_selection_popup.dart';
import 'package:ako_basma/screens/home/<USER>/components/task_card.dart';
import 'package:ako_basma/screens/tasks/components/task_details/task_details_popup.dart';

class Tasks extends StatefulWidget {
  const Tasks({super.key});

  @override
  State<Tasks> createState() => _TasksState();
}

class _TasksState extends State<Tasks> {
  bool _hasProject = false;
  bool _hasTasks = false;
  bool _showCreateTaskScreen =
      false; // New state to control the create task screen
  int _selectedProjectIndex = 0;
  final List<String> _projectList = [
    'Ako Basma',
    'Ako Basma',
    'Ako Basma',
    'Ako Basma',
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildAppBar(context),
        Expanded(
          child: _buildBody(context),
        ),
      ],
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      color: colors.background,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppBar(
            backgroundColor: colors.background,
            automaticallyImplyLeading: false,
            // titleSpacing: 13,
            title: InkWell(
              onTap: () async {
                print('Project name tapped!'); // Debug print
                showModalBottomSheet<int>(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  useRootNavigator: true,
                  builder: (context) => ProjectSelectionPopup(
                    selectedIndex: _selectedProjectIndex,
                    projects: _projectList,
                    onSelected: (index) {
                      setState(() {
                        _selectedProjectIndex = index;
                        // Update project selection logic here
                      });
                    },
                  ),
                );
              },
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Flexible(
                      child: Text(
                        _hasProject
                            ? _projectList[_selectedProjectIndex]
                            : Labels.allProjects,
                        style: TextStyle(
                          color: colors.primaryText,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(left: 4),
                    ),
                    Icon(
                      Icons.keyboard_arrow_down,
                      color: colors.primary,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
            titleSpacing: 0,
            leadingWidth: 13,
            leading: Container(
              margin: const EdgeInsets.only(left: 6),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.only(right: 14, top: 6),
                decoration: BoxDecoration(
                  color: colors.backgroundContainer,
                  borderRadius: BorderRadius.circular(6.67),
                  border: Border.all(
                    color: colors.primaryVariant,
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: () => _hasProject && !_showCreateTaskScreen
                      ? _showAddTaskPopup(context)
                      : _showCreateProjectPopup(context),
                  icon: SvgPicture.asset(
                    'assets/icons/tasks_screen/add.svg',
                    colorFilter: ColorFilter.mode(
                      colors.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ],
          ),
          Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.only(left: 20, bottom: 8),
            child: Text(
              _hasProject && !_showCreateTaskScreen
                  ? 'Manage Tasks'
                  : Labels.manageProjects,
              style: textStyles.body3.copyWith(
                color: colors.tertiaryText,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showCreateProjectPopup(BuildContext context) async {
    final bool? projectCreated = await showAdaptivePopup<bool>(
      context,
      (ctx, sc) => CreateProject(
        onBack: () => Navigator.pop(ctx),
        onProjectCreated: () {
          // legacy callback (not used now)
        },
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );

    if (projectCreated == true && mounted) {
      setState(() {
        _hasProject = true;
        _showCreateTaskScreen =
            true; // Show the create task screen instead of popup
      });
    }
  }

  Future<void> _showAddTaskPopup(BuildContext context) async {
    final bool? taskCreated = await showAdaptivePopup<bool>(
      context,
      (ctx, sc) => AddTaskPopup(
        onBack: () => Navigator.pop(ctx),
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );

    if (taskCreated == true && mounted) {
      setState(() {
        _hasTasks = true;
        _showCreateTaskScreen =
            false; // Hide create task screen and show tasks list
      });
    }
  }

  Widget _buildBody(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    if (!_hasProject) {
      // Show empty state for no projects
      return Container(
        color: colors.background,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Empty state icon
              SvgPicture.asset(
                'assets/images/server.svg',
                width: 64,
                height: 64,
                colorFilter: ColorFilter.mode(
                  colors.tertiaryText,
                  BlendMode.srcIn,
                ),
              ),
              Container(
                padding: const EdgeInsets.only(top: 16),
              ),
              Text(
                Labels.noProjectFound,
                style: textStyles.headline4.copyWith(
                  color: colors.primaryText,
                ),
              ),
              Container(
                padding: const EdgeInsets.only(top: 8),
              ),
              Text(
                Labels.pleaseCreateAProject,
                style: textStyles.body2.copyWith(
                  color: colors.secondaryText,
                ),
              ),
              Container(
                padding: const EdgeInsets.only(top: 24),
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 110),
                height: 40,
                child: ElevatedButton.icon(
                  onPressed: () => _showCreateProjectPopup(context),
                  icon: SvgPicture.asset(
                    'assets/icons/tasks_screen/add.svg',
                    width: 24,
                    height: 24,
                    colorFilter: ColorFilter.mode(
                      theme.colorScheme.onPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                  label: Text(
                    Labels.createProject,
                    style: textStyles.headline4.copyWith(
                      color: theme.colorScheme.onPrimary,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colors.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                    minimumSize: const Size.fromHeight(40),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (_showCreateTaskScreen) {
      // Show the CreateTask screen (not popup) - this is the screen with "Create Task" button
      return CreateTask(
        onBack: () {
          setState(() {
            _showCreateTaskScreen = false;
            _hasProject = false; // Reset to show create project screen again
          });
        },
        onTaskCreated: () {
          setState(() {
            _hasTasks = true;
            _showCreateTaskScreen = false;
          });
        },
      );
    } else if (!_hasTasks) {
      // Show empty state for no tasks
      return Container(
        color: colors.background,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Empty state icon
              SvgPicture.asset(
                'assets/images/server.svg',
                width: 64,
                height: 64,
                colorFilter: ColorFilter.mode(
                  colors.tertiaryText,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'No Tasks Found',
                style: textStyles.body.copyWith(
                  color: colors.primaryText,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Please create a task to get started',
                style: textStyles.body.copyWith(
                  color: colors.secondaryText,
                ),
              ),
              const SizedBox(height: 24),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 80),
                child: ElevatedButton.icon(
                  onPressed: () => _showAddTaskPopup(context),
                  icon: Icon(Icons.add,
                      size: 20, color: theme.colorScheme.onPrimary),
                  label: Text(
                    'Create Task',
                    style: textStyles.body.copyWith(
                      color: theme.colorScheme.onPrimary,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colors.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                    minimumSize: const Size(double.infinity, 48),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // Show tasks list which are my task card as of now
      return Container(
        color: colors.background,
        child: Column(
          children: [
            // Search bar
            Container(
              margin: const EdgeInsets.fromLTRB(16, 12, 16, 12),
              child: CustomSearchBar(
                hintText: 'Search for a task',
                margin: EdgeInsets.zero,
                onChanged: (value) {
                  print(value);
                },
              ),
            ),

            // Tasks list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                itemCount: 5, // For now, show 5 sample tasks
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      showAdaptivePopup(
                        context,
                        (ctx, sc) => TaskDetailsPopup(
                          onBack: () => Navigator.pop(ctx),
                        ),
                        isDismissible: true,
                        scrollable: true,
                        contentPadding: EdgeInsets.zero,
                        topRadius: 0,
                        fullScreen: true,
                        useRootNavigator: true,
                      );
                    },
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 4),
                      child: const TaskCard(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    }
  }
}
