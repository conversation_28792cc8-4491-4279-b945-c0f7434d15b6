import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AccInfo extends StatefulWidget {
  const AccInfo({super.key});

  @override
  State<AccInfo> createState() => _AccInfoState();
}

class _AccInfoState extends State<AccInfo> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController emailController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      padding: const EdgeInsets.fromLTRB(10, 10, 10, 8),
      child: Column(
        children: [
          // Account title
          Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 16),
            child: Text(
              Labels.accountInfo,
              style: textStyles.body.copyWith(
                color: colors.primaryText,
              ),
            ),
          ),
          // Title field
          TextField(
            controller: nameController,
            decoration: InputDecoration(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              labelText: 'Name',
              floatingLabelBehavior: FloatingLabelBehavior.auto,
              labelStyle: textStyles.body2.copyWith(
                color: colors.tertiaryText,
              ),
              filled: true,
              fillColor: colors.backgroundContainer,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.strokeColor,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.primary,
                ),
              ),
            ),
            style: textStyles.body2.copyWith(
              color: colors.primaryText,
            ),
          ),

          Container(
            height: 8,
          ),

          // Description field
          TextField(
            controller: phoneNumberController,
            maxLines: 1,
            decoration: InputDecoration(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              labelText: Labels.phoneNumber,
              floatingLabelBehavior: FloatingLabelBehavior.auto,
              labelStyle: textStyles.body2.copyWith(
                color: colors.tertiaryText,
              ),
              filled: true,
              fillColor: colors.backgroundContainer,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.strokeColor,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.primary,
                ),
              ),
            ),
            style: textStyles.body2.copyWith(
              color: colors.primaryText,
            ),
          ),

          Container(
            height: 8,
          ),

          // Amount field
          TextField(
            controller: emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: InputDecoration(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              labelText: Labels.emailAddress,
              floatingLabelBehavior: FloatingLabelBehavior.auto,
              labelStyle: textStyles.body2.copyWith(
                color: colors.tertiaryText,
              ),
              filled: true,
              fillColor: colors.backgroundContainer,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.strokeColor,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.primary,
                ),
              ),
            ),
            style: textStyles.body2.copyWith(
              color: colors.primaryText,
            ),
          ),
        ],
      ),
    );
  }
}
