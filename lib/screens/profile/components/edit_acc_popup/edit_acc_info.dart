import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/profile/components/edit_acc_popup/acc_info.dart';
import 'package:ako_basma/screens/profile/components/edit_acc_popup/not_editable.dart';
import 'package:ako_basma/screens/profile/components/edit_acc_popup/other_info.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class EditAccInfo extends StatefulWidget {
  final VoidCallback? onBack;

  const EditAccInfo({super.key, this.onBack});

  @override
  State<EditAccInfo> createState() => _EditAccInfoState();
}

class _EditAccInfoState extends State<EditAccInfo> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          // Header with back button and title
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    if (widget.onBack != null) {
                      widget.onBack!();
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.arrow_back_ios_new,
                      color: colors.primaryText,
                      size: 20,
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(left: 12),
                ),
                Text(
                  Labels.editAccountInfo,
                  style: textStyles.headline4.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          ),
          // Main content - scrollable
          Expanded(
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    AccInfo(),
                    NotEditable(),
                    OtherInfo(),
                  ],
                ),
              ),
            ),
          ),

          // Save button at bottom
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            margin: const EdgeInsets.only(bottom: 24),
            child: Container(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: colors.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Text(
                  Labels.save,
                  style: textStyles.headline2.copyWith(
                    color: theme.colorScheme.onPrimary,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
