import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/profile/components/settings/chat_with_HR.dart';
import 'package:ako_basma/screens/profile/components/settings/custom_settings_tile.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class Settings extends StatefulWidget {
  const Settings({super.key});

  @override
  State<StatefulWidget> createState() {
    return _SettingsState();
  }
}

class _SettingsState extends State<Settings> {
  // State variables for toggle switches
  bool _themeModeEnabled = false;
  bool _notificationsEnabled = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Labels.settings,
            style: textStyles.headline4.copyWith(
              color: colors.secondaryText,
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 8),
            child: Column(
              children: [
                CustomSettingsTile(
                  title: Labels.notifications,
                  hasToggle: true,
                  toggleValue: _notificationsEnabled,
                  onToggleChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                  },
                ),
                Container(
                  margin: const EdgeInsets.only(top: 8),
                ),
                // Theme mode tile (with toggle)
                CustomSettingsTile(
                  title: Labels.themeMode,
                  hasToggle: true,
                  toggleValue: _themeModeEnabled,
                  onToggleChanged: (value) {
                    setState(() {
                      _themeModeEnabled = value;
                    });
                  },
                ),
                Container(
                  margin: const EdgeInsets.only(top: 8),
                ),
                // Language tile (no toggle)
                Container(
                  height: 56,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.primaryVariant,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          Labels.language,
                          style: textStyles.body.copyWith(
                            color: colors.primaryText,
                          ),
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(top: 4),
                        child: DropdownButton<String>(
                          value: 'English', // Default value
                          icon: Icon(
                            Icons.keyboard_arrow_down,
                            color: colors.primary,
                          ),
                          underline: Container(),
                          style: textStyles.body2.copyWith(
                            color: colors.tertiaryText,
                          ),
                          dropdownColor: colors.backgroundContainer,
                          items: const [
                            DropdownMenuItem(
                              value: 'English',
                              child: Text('English'),
                            ),
                            DropdownMenuItem(
                              value: 'Arabic',
                              child: Text('العربية'),
                            ),
                          ],
                          onChanged: (String? newValue) {
                            if (newValue != null) {
                              // Handle language change
                              print('Language changed to: $newValue');
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(top: 8),
                ),
                // Report A Problem tile (no toggle)
                CustomSettingsTile(
                  title: Labels.reportAProblem,
                  onTap: () {
                    // Handle report a problem tap
                    print('Report A Problem tapped');
                  },
                ),
                Container(
                  margin: const EdgeInsets.only(top: 8),
                ),
                // Chat With HR tile (no toggle)
                CustomSettingsTile(
                  title: Labels.chatWithHR,
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return const ChatWithHR();
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
