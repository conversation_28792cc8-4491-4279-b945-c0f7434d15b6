import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

class CustomSettingsTile extends StatelessWidget {
  final String title;
  final bool hasToggle;
  final bool? toggleValue;
  final Function(bool)? onToggleChanged;
  final VoidCallback? onTap;
  final Color? titleColor;

  const CustomSettingsTile({
    super.key,
    required this.title,
    this.hasToggle = false,
    this.toggleValue,
    this.onToggleChanged,
    this.onTap,
    this.titleColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return GestureDetector(
      onTap: hasToggle ? null : onTap,
      child: Container(
        height: 56, // Fixed height for all tiles
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: colors.primaryVariant,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: textStyles.body.copyWith(
                  color: titleColor ?? colors.primaryText,
                ),
              ),
            ),
            if (hasToggle)
              Transform.scale(
                scale: 0.8,
                child: CupertinoSwitch(
                  value: toggleValue ?? false,
                  onChanged: onToggleChanged,
                  activeColor: colors.primary,
                  thumbColor: theme.colorScheme.onPrimary,
                  trackColor: colors.strokeColor,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
