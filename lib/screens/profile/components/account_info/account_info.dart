import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/profile/components/account_info/details.dart';
import 'package:ako_basma/screens/profile/components/account_info/edit_info_button.dart';
import 'package:ako_basma/screens/profile/components/account_info/name.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

Widget accountInfo(BuildContext context) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  return Container(
    margin: const EdgeInsets.only(top: 8),
    height: 260,
    width: double.infinity,
    decoration: BoxDecoration(
      color: colors.backgroundContainer,
      border: Border.all(
        color: colors.strokeColor,
        width: 1,
      ),
      borderRadius: BorderRadius.circular(8),
    ),
    child: const Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.all(12),
          child: Name(),
        ),
        Details(),
        EditInfoButton(),
      ],
    ),
  );
}
