import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/profile/components/edit_acc_popup/edit_acc_info.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';

class EditInfoButton extends StatefulWidget {
  const EditInfoButton({super.key});

  @override
  State<EditInfoButton> createState() => _EditInfoButtonState();
}

class _EditInfoButtonState extends State<EditInfoButton> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 6, left: 16, right: 16),
      child: OutlinedButton(
        onPressed: () {
          showAdaptivePopup(
            context,
            (ctx, sc) => EditAccInfo(
              onBack: () => Navigator.pop(ctx),
            ),
            isDismissible: false,
            scrollable: true,
            contentPadding: EdgeInsets.zero,
            topRadius: 0,
            fullScreen: true,
            useRootNavigator: true,
          );
        },
        style: OutlinedButton.styleFrom(
          foregroundColor: colors.secondaryText,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          side: BorderSide(
            color: colors.strokeColor,
            width: 1,
          ),
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
        ),
        child: Text(
          Labels.editAccountInfo,
          style: textStyles.body.copyWith(
            color: colors.secondaryText,
          ),
        ),
      ),
    );
  }
}
