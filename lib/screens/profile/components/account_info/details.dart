import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class Details extends StatefulWidget {
  const Details({super.key});

  @override
  State<Details> createState() => _DetailsState();
}

class _DetailsState extends State<Details> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          details(context, Labels.email, '<EMAIL>'),
          details(context, Labels.phone, '+1234567890'),
          details(context, Labels.dob, '01/12/2004'),
          details(context, Labels.startDate, '01/01/2020'),
        ],
      ),
    );
  }
}

Widget details(
    BuildContext context, String timeDurationText, String actualTime) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 13, vertical: 4),
    width: double.infinity,
    child: Container(
      padding: const EdgeInsets.fromLTRB(8, 4, 8, 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
        color: colors.background,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              timeDurationText,
              style: textStyles.body3.copyWith(
                color: colors.secondaryText,
              ),
            ),
          ),
          Text(
            actualTime,
            style: textStyles.body3.copyWith(
              color: colors.secondaryText,
            ),
          ),
        ],
      ),
    ),
  );
}
