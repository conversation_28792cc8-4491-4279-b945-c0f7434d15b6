import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/profile/components/other/logout.dart';
import 'package:ako_basma/screens/profile/components/other/resignation_request/resignation_request.dart';
import 'package:ako_basma/screens/profile/components/settings/custom_settings_tile.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/util/ui/popups.dart';

class Other extends StatefulWidget {
  const Other({super.key});

  @override
  State<StatefulWidget> createState() {
    return _OtherState();
  }
}

class _OtherState extends State<Other> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Labels.other,
            style: textStyles.headline4.copyWith(
              color: colors.secondaryText,
            ),
          ),
          // Resignation request button
          Container(
            margin: const EdgeInsets.only(top: 8),
            child: GestureDetector(
              onTap: () {
                showAdaptivePopup(
                  context,
                  (ctx, sc) => ResignationRequest(
                    onBack: () => Navigator.pop(ctx),
                  ),
                  isDismissible: false,
                  scrollable: true,
                  contentPadding: EdgeInsets.zero,
                  topRadius: 0,
                  fullScreen: true,
                  useRootNavigator: true,
                );
              },
              child: const CustomSettingsTile(
                title: Labels.resignationRequest,
                hasToggle: false,
              ),
            ),
          ),

          // Logout button
          Container(
            margin: const EdgeInsets.only(top: 8),
            child: GestureDetector(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return const Logout();
                  },
                );
              },
              child: CustomSettingsTile(
                title: Labels.logout,
                hasToggle: false,
                titleColor: colors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
