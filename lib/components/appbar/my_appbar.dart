import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:solar_icons/solar_icons.dart';

class MyAppbar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onBack;
  final List<Widget>? actions;

  const MyAppbar({
    super.key,
    required this.title,
    this.onBack,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final canPop = Navigator.of(context).canPop();
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return SafeArea(
      bottom: false,
      child:
          // Container(
          //   padding: const EdgeInsets.all(16),
          //   child: Row(
          //     children: [
          //       if (canPop)
          //         GestureDetector(
          //           onTap: onBack ?? () => Navigator.of(context).maybePop(),
          //           child: Container(
          //             padding: const EdgeInsets.all(8),
          //             decoration: BoxDecoration(
          //               color: colors.backgroundContainer,
          //               borderRadius: BorderRadius.circular(8),
          //               border: Border.all(
          //                 color: colors.strokeColor,
          //                 width: 1,
          //               ),
          //             ),
          //             child: Icon(
          //               Icons.arrow_back_ios_new,
          //               color: colors.primaryText,
          //               size: 20,
          //             ),
          //           ),
          //         ),
          //       Container(
          //         margin: const EdgeInsets.only(left: 12),
          //       ),
          //       Text(
          //         title,
          //         style: textStyles.headline3.copyWith(
          //           color: colors.secondaryText,
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          Container(
        height: preferredSize.height,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        // decoration: BoxDecoration(
        //   color: Theme.of(context).appBarTheme.backgroundColor ??
        //       Theme.of(context).colorScheme.primary,
        //   boxShadow: [
        //     BoxShadow(
        //       color: Colors.black.withOpacity(0.05),
        //       blurRadius: 6,
        //       offset: const Offset(0, 3),
        //     )
        //   ],
        // ),
        child: Row(
          children: [
            if (canPop)
              GestureDetector(
                onTap: onBack ?? () => Navigator.of(context).maybePop(),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    SolarIconsOutline.altArrowLeft,
                    color: colors.primaryText,
                    // textDirection: TextDirection.ltr,
                    size: 32,
                  ),
                ),
              ), // spacing for alignment
            Flexible(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Align(
                    alignment: AlignmentDirectional.centerStart,
                    child: Text(
                      title,
                      style: textStyles.headline3.copyWith(
                        color: colors.secondaryText,
                      ),
                      textAlign: TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
            ),
            if (actions != null) ...actions!,
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
