import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'util.dart';

class SimpleTextField extends StatelessWidget {
  const SimpleTextField({
    super.key,
    this.heading,
    this.controller,
    this.headingPadding = 5,
    this.initialValue,
    this.decoration,
    this.keyboardType,
    this.textCapitalization = TextCapitalization.none,
    this.textInputAction,
    this.style,
    this.strutStyle,
    this.textDirection,
    this.textAlign = TextAlign.start,
    this.textAlignVertical,
    this.autofocus = false,
    this.readOnly = false,
    this.showCursor,
    this.obscuringCharacter = '•',
    this.obscureText = false,
    this.autocorrect = true,
    this.enableSuggestions = true,
    this.maxLengthEnforcement,
    this.maxLines = 1,
    this.minLines,
    this.expands = false,
    this.maxLength,
    this.onChanged,
    this.onEditingComplete,
    this.onFieldSubmitted,
    this.onSaved,
    this.onTap,
    this.validator,
    this.inputFormatters,
    this.enabled,
    this.cursorColor,
    this.keyboardAppearance,
    this.buildCounter,
    this.scrollPhysics,
    this.autofillHints,
    this.clipBehavior = Clip.hardEdge,
    this.autovalidateMode,
    this.focusNode,
    this.fieldKey,
  });
  final Key? fieldKey;
  final String? initialValue;
  final InputDecoration? decoration;
  final TextInputType? keyboardType;
  final TextCapitalization textCapitalization;
  final TextInputAction? textInputAction;
  final TextStyle? style;
  final StrutStyle? strutStyle;
  final TextDirection? textDirection;
  final TextAlign textAlign;
  final TextAlignVertical? textAlignVertical;
  final bool autofocus;
  final bool readOnly;
  final bool? showCursor;
  final String obscuringCharacter;
  final bool obscureText;
  final bool autocorrect;
  final bool enableSuggestions;
  final MaxLengthEnforcement? maxLengthEnforcement;
  final int? maxLines;
  final int? minLines;
  final bool expands;
  final int? maxLength;

  final void Function(String)? onChanged;
  final void Function()? onTap;
  final void Function()? onEditingComplete;
  final void Function(String)? onFieldSubmitted;
  final void Function(String?)? onSaved;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final bool? enabled;
  final Color? cursorColor;
  final Brightness? keyboardAppearance;
  final Widget? Function(
    BuildContext, {
    required int currentLength,
    required bool isFocused,
    required int? maxLength,
  })? buildCounter;
  final ScrollPhysics? scrollPhysics;
  final Iterable<String>? autofillHints;
  final Clip clipBehavior;

  final Widget? heading;
  final TextEditingController? controller;
  final double headingPadding;
  final FocusNode? focusNode;
  final AutovalidateMode? autovalidateMode;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final baseInputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
    );

    //configure acc to app style.
    final effDecoration = mergeInputDecoration(
        InputDecoration(
            floatingLabelBehavior: FloatingLabelBehavior.auto,
            labelStyle: textStyles.body2.copyWith(color: colors.tertiaryText),
            // textStyles.body3.copyWith
            floatingLabelStyle: WidgetStateTextStyle.resolveWith((states) {
              if (states.contains(WidgetState.error)) {
                return textStyles.body2.copyWith(color: colors.error);
              }

              return textStyles.body2.copyWith(color: colors.tertiaryText);
            }),
            // TextStyle(
            //   color: WidgetStateColor.resolveWith((states) {
            //     print(states);
            //     if (states.contains(WidgetState.error)) return colors.error;
            //     return colors.tertiaryText;
            //   }),
            // ),

            hintStyle: textStyles.body2.copyWith(color: colors.primary),
            prefixIconColor: TextFieldStateColor(
              defaultColor: (controller?.text.trim().isNotEmpty ?? false)
                  ? colors.primaryText
                  : colors.tertiaryText,
              disabledColor: colors.disabled,
              errorColor: colors.error,
              focusedColor: colors.primary,
            ),
            suffixIconColor: TextFieldStateColor(
              defaultColor: colors.tertiaryText,
              disabledColor: colors.disabled,
              errorColor: colors.error,
              focusedColor: colors.primary,
            ),

            // borders
            border: baseInputBorder.copyWith(
              borderSide: BorderSide(
                color: colors.strokeColor,
                width: 1,
              ),
            ),
            enabledBorder: baseInputBorder.copyWith(
              borderSide: BorderSide(
                color: colors.strokeColor,
                width: 1,
              ),
            ),
            focusedBorder: baseInputBorder.copyWith(
              borderSide: BorderSide(
                color: colors.primary,
                width: 1,
              ),
            ),
            errorBorder: baseInputBorder.copyWith(
              borderSide: BorderSide(
                color: colors.error,
                width: 1,
              ),
            ),
            focusedErrorBorder: baseInputBorder.copyWith(
              borderSide: BorderSide(
                color: colors.error,
                width: 1,
              ),
            ),
            fillColor: colors.backgroundContainer,
            filled: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 22),
            isDense: false,
            errorStyle:
                textStyles.body3.copyWith(color: colors.error, fontSize: 12)
            // prefixIconConstraints:
            //     const BoxConstraints(minWidth: 40, maxWidth: 40),
            // suffixIconConstraints:
            //     const BoxConstraints(minWidth: 40, maxWidth: 40),

            ),
        decoration);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (heading != null)
          Padding(
            padding: EdgeInsets.only(
              bottom: headingPadding,
            ),
            child: heading,
          ),
        TextFormField(
          key: fieldKey,
          autovalidateMode: autovalidateMode,
          focusNode: focusNode,

          controller: controller,
          decoration: effDecoration,
          onChanged: onChanged,
          initialValue: initialValue,
          keyboardType: keyboardType,
          textCapitalization: textCapitalization,
          textInputAction: textInputAction,
          style: style ?? textStyles.body2.copyWith(color: colors.primaryText),
          // AppTextStyle.title,
          // widget.style?.copyWith(
          //   // color: Colors.white,
          //   fontSize: 14,
          //   fontWeight: FontWeight.w500,
          // ),
          strutStyle: strutStyle,
          textDirection: textDirection,
          textAlign: textAlign,
          textAlignVertical: textAlignVertical,
          autofocus: autofocus,
          readOnly: readOnly,
          showCursor: showCursor,
          obscuringCharacter: obscuringCharacter,
          obscureText: obscureText,
          autocorrect: autocorrect,
          enableSuggestions: enableSuggestions,
          maxLengthEnforcement: maxLengthEnforcement,
          maxLines: maxLines,
          minLines: minLines,
          expands: expands,
          maxLength: maxLength,
          onTap: onTap,
          onEditingComplete: onEditingComplete,
          onFieldSubmitted: onFieldSubmitted,
          onSaved: onSaved,
          validator: validator,
          inputFormatters: inputFormatters,
          enabled: enabled,
          cursorColor: cursorColor,
          keyboardAppearance: keyboardAppearance,
          buildCounter: buildCounter,
          scrollPhysics: scrollPhysics,
          autofillHints: autofillHints,
          clipBehavior: clipBehavior,
        ),
      ],
    );
  }
}

class TextFieldStateColor extends WidgetStateColor {
  const TextFieldStateColor({
    required this.defaultColor,
    required this.disabledColor,
    required this.errorColor,
    required this.focusedColor,
  }) : super(0);

  final Color defaultColor;
  final Color disabledColor;
  final Color errorColor;
  final Color focusedColor;

  @override
  Color resolve(Set<WidgetState> states) {
    if (states.contains(WidgetState.error)) return errorColor;
    if (states.contains(WidgetState.disabled)) return disabledColor;
    if (states.contains(WidgetState.focused)) return focusedColor;
    return defaultColor;
  }
}
