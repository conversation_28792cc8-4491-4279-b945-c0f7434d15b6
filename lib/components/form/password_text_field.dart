import 'package:ako_basma/styles/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:remixicon/remixicon.dart';
import '/components/form/util.dart';

class PasswordTextField extends StatefulWidget {
  const PasswordTextField({
    super.key,
    this.heading,
    this.controller,
    this.headingPadding = 8,
    this.initialValue,
    this.decoration,
    this.keyboardType,
    this.textCapitalization = TextCapitalization.none,
    this.textInputAction,
    this.style,
    this.strutStyle,
    this.textDirection,
    this.textAlign = TextAlign.start,
    this.textAlignVertical,
    this.autofocus = false,
    this.readOnly = false,
    this.showCursor,
    this.obscuringCharacter = '•',
    this.autocorrect = false,
    this.enableSuggestions = true,
    this.maxLengthEnforcement,
    this.maxLines = 1,
    this.minLines,
    this.expands = false,
    this.maxLength,
    this.onChanged,
    this.onEditingComplete,
    this.onFieldSubmitted,
    this.onSaved,
    this.validator,
    this.inputFormatters,
    this.enabled,
    this.cursorColor,
    this.keyboardAppearance,
    this.buildCounter,
    this.scrollPhysics,
    this.autofillHints,
    this.clipBehavior = Clip.hardEdge,
    this.autovalidateMode,
  });

  final String? initialValue;
  final InputDecoration? decoration;
  final TextInputType? keyboardType;
  final TextCapitalization textCapitalization;
  final TextInputAction? textInputAction;
  final TextStyle? style;
  final StrutStyle? strutStyle;
  final TextDirection? textDirection;
  final TextAlign textAlign;
  final TextAlignVertical? textAlignVertical;
  final bool autofocus;
  final bool readOnly;
  final bool? showCursor;
  final String obscuringCharacter;
  final bool autocorrect;
  final bool enableSuggestions;
  final MaxLengthEnforcement? maxLengthEnforcement;
  final int? maxLines;
  final int? minLines;
  final bool expands;
  final int? maxLength;
  final void Function(String)? onChanged;
  final void Function()? onEditingComplete;
  final void Function(String)? onFieldSubmitted;
  final void Function(String?)? onSaved;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final bool? enabled;
  final Color? cursorColor;
  final Brightness? keyboardAppearance;
  final Widget? Function(
    BuildContext, {
    required int currentLength,
    required bool isFocused,
    required int? maxLength,
  })? buildCounter;
  final ScrollPhysics? scrollPhysics;
  final Iterable<String>? autofillHints;
  final Clip clipBehavior;

  final Widget? heading;
  final TextEditingController? controller;
  final double headingPadding;
  final AutovalidateMode? autovalidateMode;

  @override
  State<PasswordTextField> createState() => _PasswordTextFieldState();
}

class _PasswordTextFieldState extends State<PasswordTextField> {
  bool _visible = false;
  @override
  Widget build(BuildContext context) {
    final suffix = IconButton(
      onPressed: _toggle,
      icon: _visible
          ? Icon(
              Remix.eye_line,
              size: 20,
              color: colors(context).secondary,
            )
          : Icon(
              Remix.eye_off_line,
              size: 20,
              color: colors(context).secondary,
            ),
    );
    final decoration = mergeInputDecoration(
        InputDecoration(
          // enabledBorder: OutlineInputBorder(
          //   borderRadius: BorderRadius.circular(16),
          //   borderSide: const BorderSide(
          //     width: 1,
          //     style: BorderStyle.solid,
          //     color: Color(0xff384860),
          //   ),
          // ),
          // focusedBorder: OutlineInputBorder(
          //   borderRadius: BorderRadius.circular(16),
          //   borderSide: const BorderSide(
          //     color: AppColors.primary200,
          //     width: 2,
          //   ),
          // ),
          // focusedErrorBorder: OutlineInputBorder(
          //   borderRadius: BorderRadius.circular(16),
          //   borderSide: const BorderSide(
          //     color: Colors.red,
          //     width: 2,
          //   ),
          // ),
          // errorBorder: OutlineInputBorder(
          //   borderRadius: BorderRadius.circular(16),
          //   borderSide: BorderSide(
          //     color: Theme.of(context).colorScheme.error,
          //     width: 2,
          //   ),
          // ),
          // hintStyle: AppTextStyle.subtitle,
          // contentPadding: const EdgeInsets.only(
          //   top: 16,
          //   left: 16,
          //   right: 16,
          //   bottom: 16,
          // ),
          suffixIcon: suffix,
        ),
        widget.decoration);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.heading != null)
          Padding(
            padding: EdgeInsets.only(
              bottom: widget.headingPadding,
            ),
            child: widget.heading,
          ),
        TextFormField(
          controller: widget.controller,
          autovalidateMode: widget.autovalidateMode,
          decoration: decoration,
          initialValue: widget.initialValue,
          keyboardType: widget.keyboardType,
          textCapitalization: widget.textCapitalization,
          textInputAction: widget.textInputAction,
          style: widget.style,
          onChanged: widget.onChanged,
          strutStyle: widget.strutStyle,
          textDirection: widget.textDirection,
          textAlign: widget.textAlign,
          textAlignVertical: widget.textAlignVertical,
          autofocus: widget.autofocus,
          readOnly: widget.readOnly,
          showCursor: widget.showCursor,
          obscuringCharacter: widget.obscuringCharacter,
          obscureText: !_visible,
          autocorrect: widget.autocorrect,
          enableSuggestions: widget.enableSuggestions,
          maxLengthEnforcement: widget.maxLengthEnforcement,
          maxLines: widget.maxLines,
          minLines: widget.minLines,
          expands: widget.expands,
          maxLength: widget.maxLength,
          onEditingComplete: widget.onEditingComplete,
          onFieldSubmitted: widget.onFieldSubmitted,
          onSaved: widget.onSaved,
          validator: widget.validator,
          inputFormatters: widget.inputFormatters,
          enabled: widget.enabled,
          cursorColor: widget.cursorColor,
          keyboardAppearance: widget.keyboardAppearance,
          buildCounter: widget.buildCounter,
          scrollPhysics: widget.scrollPhysics,
          autofillHints: widget.autofillHints,
          clipBehavior: widget.clipBehavior,
        ),
      ],
    );
  }

  void _toggle() {
    setState(() {
      _visible = !_visible;
    });
  }
}
