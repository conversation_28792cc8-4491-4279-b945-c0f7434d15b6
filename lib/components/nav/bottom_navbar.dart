import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:remixicon/remixicon.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:solar_icons/solar_icons.dart';

class CustomBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.sizeOf(context).width;
    final navBarWidth = screenWidth > 360 ? 328.0 : screenWidth - 32;
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      margin: const EdgeInsets.only(bottom: 28, left: 16, right: 16),
      width: navBarWidth,
      height: 72,
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(16),
        // border: Border.all(
        //     color: colors(context).shadow.withOpacity(0.10), width: 1),
        boxShadow: const [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.1),
            blurRadius: 4,
            spreadRadius: 0,
            offset: Offset(0, 0),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildNavItem(
            context: context,
            path: 'home',
            icon: Iconsax.home,
            selectedIcon: Iconsax.home,
            label: Labels.home,
            index: 0,
          ),
          _buildNavItem(
            context: context,
            path: 'Chat',
            icon: SolarIconsOutline.dialog,
            selectedIcon: SolarIconsBold.dialog,
            label:
                // localize
                'Chat',
            index: 1,
          ),
          _buildNavItem(
            context: context,
            path: 'tasks',
            icon: Iconsax.menu_board_copy,
            selectedIcon: Iconsax.menu_board,
            label: Labels.tasks,
            index: 2,
          ),
          _buildNavItem(
            context: context,
            path: 'profile',
            icon: Iconsax.user_copy,
            selectedIcon: Iconsax.user,
            label: Labels.profile,
            index: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    IconData? icon,
    IconData? selectedIcon,
    required String path,
    required String label,
    required int index,
    Widget? customIcon,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final isSelected = currentIndex == index;
    final iconColor = isSelected ? colors.primary : colors.secondaryText;
    final textColor = isSelected ? colors.primary : colors.tertiaryText;

    return Expanded(
      child: InkWell(
        onTap: () => onTap(index),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              customIcon ??
                  Icon(
                    (isSelected ? selectedIcon ?? icon : icon) ??
                        FontAwesomeIcons.circleQuestion,
                    color: iconColor,
                    size: 24,
                  ),
              Container(
                margin: const EdgeInsets.only(top: 4),
                child: Text(
                  label,
                  style: textStyles.buttonSmall.copyWith(
                    color: textColor,
                  ),
                  textScaler: TextScaler.noScaling,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
