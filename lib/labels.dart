class Labels {
  // Home Screen
  static const String welcome = 'Welcome';
  static const String name = 'Nada Jaafar Uday';
  static const String timeClock = 'Time Clock';
  static const String workspace = 'Workspace';
  static const String location =
      '10 Blackstone Street, London, UK'; // will be changed according to the geolocation
  static const String clockIn = 'Clock in';
  static const String startWork = 'Start Work';
  static const String endWork = 'End Work';
  static const String notification = 'Notification';

  // Bottom Nav Bar
  static const String home = 'Home';
  static const String profile = 'Profile';
  static const String settings = 'Settings';
  static const String chat = 'Chat';
  static const String tasks = 'Tasks';

  // Auth Screen
  static const String appName = 'Ako Basma';
  static const String next = 'Next';
  static const String checkYourMessages = 'Check your messages';
  static const String verificationCode =
      'Please Enter The Verification Code Sent To +96477000000';
  static const String resendCode = 'Resend Code';

  // Clock In Details Screen
  static const String clockInTime = '10:30:00';
  static const String totalShiftTime = 'Total Shift Time';
  static const String startBreak = 'Start Break';
  static const String endShift = 'End Shift';
  static const String overTime = 'Overtime Hours';
  static const String breakTime = 'Break Time';
  static const String delay = 'Delay:';

  // Clock out button
  static const String clockOut = 'Clock Out';

  // Shift Details Screen
  static const String shiftDetails = 'Shift Details';
  static const String clockOutTime = '10:30:00';
  static const String ifYouClockOutNow =
      'If You Clock Out Now, You\'ll be Clock Out at';
  static const String totalHours = 'Total Hours';
  static const String shiftLocation = '10 Blackstone Street, London, UK';
  static const String confirmHours = 'Confirm Hours';
  static const String successfullyCompletedYourShift =
      'Successfully completed your shift';
  static const String drawSignature = 'Draw Signature';
  static const String addNote = 'Add a Note';
  static const String save = 'Save';
  static const String cancel = 'Cancel';
  static const String enterYourNoteHere = 'Enter Your Note Here';

  // Request Expenses Screen
  static const String clickToUpload = 'Click to Upload';
  static const String maxFileSize = '(Max file size 25MB)';

  // Salary Screen
  static const String thisMonthsSalary = 'This Month\'s Salary';
  static const String netSalary = 'Net Salary';
  static const String paySlip = 'Pay Slip.pdf';
  static const String maxFileSizePdf = '200 KB';

  // Workspace Screen
  static const String requestLeave = 'Request for Leave';
  static const String timesheet = 'Timesheet';
  static const String schedule = 'Schedule';
  static const String salary = 'Salary';
  static const String requestExpenses = 'Request For Expenses';
  static const String submit = 'Submit';
  static const String allDay = 'All Day';

  // My Tasks
  static const String myTasks = 'My Tasks';
  static const String showAll = 'Show All';
  static const String modelAnswer = 'Model Answer';
  static const String design = 'Design';
  static const String backlog = 'Backlog';

  // Company News
  static const String companyNews = 'Company News';
  static const String news = 'News';

  // News Detail Popup Screen
  static const String publishedBy = 'Published By HR Manager';
  static const String description =
      "We are pleased to introduce our new and improved Health Insurance Package, designed with your wellbeing in mind.\n\n"
      "As part of our ongoing commitment to support the health and security of our employees, we have partnered with a new provider to offer a more comprehensive medical plan. This updated package includes:\n\n"
      "• Enhanced hospital coverage, including specialist care and private rooms\n"
      "• Increased outpatient benefits and faster claim processing\n"
      "• Coverage for mental health and wellness services\n"
      "• Improved support for family members (spouse and children)\n"
      "• A simplified registration and claim procedure via the HR portal";
  static const String employeeOfTheMonth = 'Employee of the Month';
  static const String today0800Am = 'Today 08:00 AM';
  static const String howardTheodore = 'HOWARD THEODORE';

  // Schedule Screen
  static const String conferenceCenter = 'Innovation In Technology Conference';
  static const String conferenceDate = '15 March 2025';
  static const String conferenceTime = '10:00 AM - 2:00 PM';
  static const String conferenceLocation = 'Conference Hall - London';
  static const String confirmed = 'Confirmed';
  static const String pending = 'pending';
  static const String approve = 'Approve';
  static const String reject = 'Reject';

  // Timesheet Screen
  static const String timeSheet = 'Timesheet';
  static const String filter = 'Filter';
  static const String selectADate = 'Select a Date';
  static const String from = 'From';
  static const String to = 'To';
  static const String thisMonth = 'This Month';
  static const String thisWeek = 'This Week';
  static const String sinceStartOfYear = 'Since Start of Year';
  static const String today = 'Today';
  static const String approved = 'Approved';
  static const String customDateRange = 'Custom Date Range';
  static const String payForThisPeriod = 'Pay for this period';
  static const String timesheetShiftDetails = 'Timesheet Shift Details';
  static const String sendShiftChangeRequest = 'Send Shift Change Request';

  // Tasks Screen
  static const String allProjects = 'All Projects';
  static const String manageProjects = 'Manage Projects';
  static const String noProjectFound = 'No Project Found';
  static const String pleaseCreateAProject =
      'Please Create A Project First To Add Tasks';
  static const String createProject = 'Create a Project';
  static const String add = 'Add';
  static const String addTask = 'Add a Task';
  static const String createTask = 'Create a Task';
  static const String noTasksFound = 'No Tasks Found For This Project';
  static const String pleaseCreateATask = 'Please Create a Task';
  static const String assignTo = 'Assign To Me';
  static const String priority = 'Priority';
  static const String assign = 'Assign';
  static const String projects = 'Projects';

  // Profile Screen
  static const String role = 'UI/UX Designer';
  static const String designation = 'Design Team';
  static const String email = 'Email';
  static const String phone = 'Phone';
  static const String dob = 'Date of Birth';
  static const String startDate = 'Start Date';
  static const String editAccountInfo = 'Edit Account Info';
  static const String performance = 'Performance & Achievements';

  // Performance Metrics
  static const String completedTasks = 'Completed Tasks';
  static const String timeOffTaken = 'Time Off Taken';
  static const String expenses = 'Expenses';
  static const String holidays = 'Holidays';

  // Feedback
  static const String feedback = 'Feedback';
  static const String managersFeedback = 'Manager\'s Feedback';
  static const String feedbackTime = '8:00 AM';
  static const String feedbackMessage =
      'Excellent work on the recent project. Keep up the great effort';

  // Settings
  static const String reportAProblem = 'Report A Problem';
  static const String chatWithHR = 'Chat With HR';
  static const String notifications = 'Notifications';
  static const String themeMode = 'Theme Mode';
  static const String language = 'Language';

  // About
  static const String about = 'About';
  static const String aboutUs = 'About Us';
  static const String termsOfUse = 'Terms of Use';
  static const String send = 'Send';
  static const String requestToChatWithHR = 'Request to Chat With HR';

  // Other
  static const String other = 'Other';
  static const String logout = 'Log Out';
  static const String otherInfo = 'Other Information';

  // Edit Account Info
  static const String accountInfo = 'Account Info';
  static const String notEditable = 'Not Editable';
  static const String phoneNumber = 'Phone Number';
  static const String emailAddress = 'Email Address';

  // Expenses Popup
  static const String paid = 'Paid';
  static const String expenseTitle = 'Figma Subscription';
  static const String expensesAmount = '15,000';
  static const String expensesDate = '01/12/2024';

  // Specific Expense Card Popup
  static const String waitingManagerApproval = 'Waiting Manager Approval';

  // Resignation Request
  static const String resignationRequest = 'Resignation Request';

  // Chat Screen
  static const String unread = 'Unread';
  static const String teams = 'Teams';
  static const String all = 'All';

  // Individual Chat Screen
  static const String lastSeenOn = 'Last Seen on';
  static const String writeAMessage = 'Write a message . . .';
  static const String yesterday = 'Yesterday';
  static const String online = 'Online';
  static const String typing = 'Typing...';
  static const String tapToRecord = 'Tap to record';
  static const String messageDelivered = 'Delivered';
  static const String messageRead = 'Read';
  static const String call = 'Call';
  static const String close = 'Close';
}
